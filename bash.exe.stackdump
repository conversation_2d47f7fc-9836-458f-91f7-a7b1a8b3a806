Stack trace:
Frame         Function      Args
0007FFFFA780  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFA780, 0007FFFF9680) msys-2.0.dll+0x2118E
0007FFFFA780  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFA780  0002100469F2 (00021028DF99, 0007FFFFA638, 0007FFFFA780, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA780  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA780  00021006A545 (0007FFFFA790, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFA790, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB80B80000 ntdll.dll
7FFB7FDB0000 KERNEL32.DLL
7FFB7E220000 KERNELBASE.dll
7FFB7F1F0000 USER32.dll
7FFB7E130000 win32u.dll
7FFB7FBC0000 GDI32.dll
7FFB7E6D0000 gdi32full.dll
7FFB7E5B0000 msvcp_win.dll
7FFB7E010000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB7FF60000 advapi32.dll
7FFB7FCA0000 msvcrt.dll
7FFB80AA0000 sechost.dll
7FFB7F450000 RPCRT4.dll
7FFB7D7F0000 CRYPTBASE.DLL
7FFB7E650000 bcryptPrimitives.dll
7FFB80010000 IMM32.DLL
